from app.core.security import get_tenant_info
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from app.v1.api.chat.core.tools import LegalTools
from app.v1.api.chat.core.utils.process_najirs import _process_sources

from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_mongodb import MongoDBChatMessageHistory
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import tools_condition
from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from fastapi import APIRouter, Depends
from typing import Annotated, List, Optional, Dict, Any
import json
import asyncio
import time

logger = StructuredLogger(__name__)
router = APIRouter()

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    najir_response: str
    act_response: str  
    najir_summary_response: str
    najir_sources: List[dict]
    act_sources: List[dict]
    najir_summary_sources: List[dict]
    final_response: str

# Global caches
_TOOL_CACHE = {}

class ChatHistoryManager:
    """Manages chat history using MongoDB"""
    
    def __init__(self, current_user: CurrentUser):
        self.current_user: CurrentUser = current_user
        logger.info("💬 CHAT HISTORY MANAGER - Initialized")
    
    async def get_user_history(self, user_id: str) -> MongoDBChatMessageHistory:
        """Get chat history for specific user"""
        logger.info(f"📚 GETTING CHAT HISTORY - User: {user_id}")
        
        # Construct connection string from async client
        client = self.current_user.db.client
        address = client.address
        connection_string = f"mongodb://{address[0]}:{address[1]}"
        
        return MongoDBChatMessageHistory(
            connection_string=connection_string,
            database_name=self.current_user.db.read_db.name,
            collection_name="chat_history",
            session_id=user_id
        )
    
    async def save_interaction(self, user_id: str, query: str, response: str):
        """Save user interaction to chat history"""
        logger.info(f"💾 SAVING INTERACTION - User: {user_id}")
        
        history = await self.get_user_history(user_id)
        history.add_user_message(query)
        history.add_ai_message(response)
        
        logger.info("✅ INTERACTION SAVED - Chat history updated")

class LegalAgent:
    def __init__(self, current_user: CurrentUser):
        logger.info("🤖 AGENT INIT - Starting LegalAgent initialization")
        
        self.current_user = current_user
        self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        self.tools = []
        self.checkpointer = AsyncMongoDBSaver(
            client=current_user.db.async_client,
            db_name=current_user.db.read_db.name,
            collection_name="checkpoints"
        )
        self.chat_manager = ChatHistoryManager(current_user)
        self.graph = None
        
        self.query_refine_prompt = """
Refine user query for Nepali legal research. Translate English to Nepali if needed.  
Create optimized queries for:  
- najir_search (case law)  
- act_search (statutes)  
- najir_summary (summaries)  

User Query: {query}  

Instructions:  
- Do NOT change or rephrase the original user query itself.  
- Only refine it for better searchability while keeping meaning intact.  
- You MUST generate refined queries for all three tools.  
- Use proper Nepali legal terminology where applicable.  
"""
        logger.info("✅ AGENT INIT COMPLETE - LegalAgent initialized")

        self.synthesis_prompt = """
      You are given multiple legal reference responses for a user query.  
Sources:  
- NAJIR Response: {najir_response}  
- ACT Response: {act_response}  
- SUMMARY Response: {najir_summary_response}  

Task:  
- Carefully read all provided responses.  
- Synthesize them into one **comprehensive, unified legal analysis**.  
- The final answer must:  
  1. Directly address the user’s query: {query}  
  2. Stay strictly grounded in the provided sources (NAJIR, ACT, SUMMARY).  
  3. Avoid adding new interpretations, assumptions, or information not present in the sources.  
  4. Present the analysis clearly, structured, and logically connected.  

Output:  
A single, consolidated legal guidance derived entirely from the provided sources in Markdown format.

        """
        
        logger.info("✅ AGENT INIT COMPLETE")

    def setup_tools(self):
        """Setup tools with caching"""
        user_id = str(self.current_user.user.id)
        
        if user_id not in _TOOL_CACHE:
            logger.info(f"🔨 CREATING TOOLS - For user: {user_id}")
            legal_tools = LegalTools()
            self.tools = legal_tools.create_tools(self.current_user)
            _TOOL_CACHE[user_id] = self.tools
            logger.info(f"✅ TOOLS CACHED - Cached for user: {user_id}")
        else:
            logger.info(f"♻️  USING CACHED TOOLS - For user: {user_id}")
            self.tools = _TOOL_CACHE[user_id]

    def query_refine_node(self, state: AgentState):
        """STEP 1: Query refinement and tool preparation"""
        logger.info("🔄 STEP 1 START - Query refinement")
        
        user_query = state["messages"][-1].content
        logger.info(f"📝 USER QUERY: {user_query}")
        
        system_msg = SystemMessage(content=self.query_refine_prompt.format(query=user_query))
        messages = [system_msg] + [msg for msg in state["messages"] if not isinstance(msg, SystemMessage)]
        logger.info("🛠️  STEP 1 COMPLETE - Tools prepared for query refinement")
        llm_with_tools = self.llm.bind_tools(self.tools)
        response = llm_with_tools.invoke(messages)
        
        tool_count = len(getattr(response, 'tool_calls', []))
        logger.info(f"🛠️  STEP 1 COMPLETE - {tool_count} tool calls prepared")
        
        return {"messages": [response]}

    async def parallel_tools_node(self, state: AgentState):
        """STEP 2: Execute all tools in parallel"""
        logger.info("🔄 STEP 2 START - Parallel tools execution")
        
        last_message = state["messages"][-1]
        if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
            logger.warning("⚠️  NO TOOL CALLS - Skipping parallel execution")
            return {"messages": []}

        # Setup parallel execution
        tasks = []
        tool_call_map = {}
        
        for tool_call in last_message.tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call["args"]
            tool_id = tool_call["id"]
            
            for tool in self.tools:
                if tool.name == tool_name:
                    task = tool.arun(tool_args)
                    tasks.append(task)
                    tool_call_map[len(tasks) - 1] = (tool_name, tool_id)
                    break

        logger.info(f"🚀 EXECUTING PARALLEL - {len(tasks)} tools running")
        execution_start = time.time()
        
        # Execute all tools in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        execution_time = time.time() - execution_start
        logger.info(f"⚡ PARALLEL EXECUTION COMPLETE - {execution_time:.2f}s")

        # Process results
        najir_response = act_response = najir_summary_response = ""
        najir_sources = act_sources = najir_summary_sources = []
        tool_messages = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ TOOL ERROR - Index {i}: {result}")
                continue
                
            try:
                parsed_result = json.loads(result)
                tool_name, tool_id = tool_call_map[i]
                
                if tool_name == "najir_search":
                    najir_response = parsed_result.get('response', '')
                    najir_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 NAJIR RESULTS - Response length: {len(najir_response)}, Sources: {len(najir_sources)}")
                elif tool_name == "act_search":
                    act_response = parsed_result.get('response', '')
                    act_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 ACT RESULTS - Response length: {len(act_response)}, Sources: {len(act_sources)}")
                elif tool_name == "najir_summary":
                    najir_summary_response = parsed_result.get('response', '')
                    najir_summary_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 SUMMARY RESULTS - Response length: {len(najir_summary_response)}, Sources: {len(najir_summary_sources)}")
                
                from langchain_core.messages import ToolMessage
                tool_messages.append(ToolMessage(
                    content=f"{tool_name} completed",
                    tool_call_id=tool_id
                ))
            except Exception as e:
                logger.error(f"❌ RESULT PROCESSING ERROR - {e}")
        
        logger.info("✅ STEP 2 COMPLETE - All tools processed")
        
        return {
            "messages": tool_messages,
            "najir_response": najir_response,
            "act_response": act_response,
            "najir_summary_response": najir_summary_response,
            "najir_sources": najir_sources,
            "act_sources": act_sources,
            "najir_summary_sources": najir_summary_sources
        }

    def synthesis_node(self, state: AgentState):
        """STEP 3: Synthesize all responses"""
        logger.info("🔄 STEP 3 START - Response synthesis")
        
        user_query = state["messages"][0].content
        
        synthesis_prompt = self.synthesis_prompt.format(
            query=user_query,
            najir_response=state.get("najir_response", ""),
            act_response=state.get("act_response", ""),
            najir_summary_response=state.get("najir_summary_response", "")
        )
        
        synthesis_start = time.time()
        final_response = self.llm.invoke([SystemMessage(content=synthesis_prompt)])
        synthesis_time = time.time() - synthesis_start
        
        logger.info(f"🎯 SYNTHESIS COMPLETE - Generated in {synthesis_time:.2f}s")
        logger.info(f"📝 FINAL RESPONSE LENGTH - {len(final_response.content)} characters")
        logger.info("✅ STEP 3 COMPLETE - Final response ready")
        
        return {
            "messages": [final_response],
            "final_response": final_response.content
        }

    def build_graph(self):
        """Build 3-step workflow graph"""
        logger.info("🏗️  BUILDING WORKFLOW - Creating 3-step graph")
        
        builder = StateGraph(AgentState)
        
        builder.add_node("query_refine", self.query_refine_node)
        builder.add_node("parallel_tools", self.parallel_tools_node)
        builder.add_node("synthesis", self.synthesis_node)
        
        builder.add_edge(START, "query_refine")
        builder.add_conditional_edges(
            "query_refine",
            tools_condition,
            {"tools": "parallel_tools", "__end__": "synthesis"}
        )
        builder.add_edge("parallel_tools", "synthesis")
        
        self.graph = builder.compile(checkpointer=self.checkpointer)
        
        logger.info("✅ WORKFLOW BUILT - 3-step graph ready with MongoDB checkpoints")
        return self.graph

    async def run(self, query: str, thread_id: Optional[str] = None):
        """Execute 3-step workflow with MongoDB persistence"""
        workflow_start = time.time()
        user_id = str(self.current_user.user.id)
        
        logger.info("🚀 WORKFLOW START - Beginning legal research")
        logger.info(f"👤 USER: {user_id}")
        logger.info(f"🔗 THREAD: {thread_id or user_id}")
        logger.info(f"❓ QUERY: {query}")
        
        if not self.tools:
            self.setup_tools()
        if not self.graph:
            self.build_graph()
        
        config = {"configurable": {"thread_id": thread_id or user_id}}
        
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "najir_response": "",
            "act_response": "",
            "najir_summary_response": "",
            "najir_sources": [],
            "act_sources": [],
            "najir_summary_sources": [],
            "final_response": ""
        }
        
        # Execute workflow
        result = await self.graph.ainvoke(initial_state, config=config)
        
        # Save to chat history
        final_response = result.get("final_response", "")
        await self.chat_manager.save_interaction(user_id, query, final_response)
        
        workflow_time = time.time() - workflow_start
        logger.info(f"🏁 WORKFLOW COMPLETE - Total time: {workflow_time:.2f}s")
        
        return result

@router.post("/agent")
async def legal_agent_chat(
    query: str,
    current_user: CurrentUser = Depends(get_tenant_info)
):
    """Legal research agent with MongoDB persistence"""

    # print("legal_agent_chat",await current_user.db.async_client.address)
    # raise NotImplementedError("This endpoint is not implemented yet.")
    request_start = time.time()
    
    logger.info("📡 API REQUEST START")
    logger.info(f"👤 USER: {current_user.user.id}")
    logger.info(f"❓ QUERY: {query}")
    
    try:
        agent = LegalAgent(current_user)
        result = await agent.run(query)
        
        request_time = time.time() - request_start
        najir_sources : Dict[str, Any] =await _process_sources(result.get("najir_sources", []), current_user)
        
        response_data = {
            "final_response": result.get("final_response", ""),
            "najir_response": result.get("najir_response", ""),
            "act_response": result.get("act_response", ""),
            "najir_sources": najir_sources.get("sources", []),
            "act_sources": result.get("act_sources", []),
            "suggested_sources": result.get("najir_summary_sources", []),
            "total_filtered": len(result.get("najir_sources", [])) + len(result.get("act_sources", [])),
            "total_suggestions": len(result.get("najir_summary_sources", [])),
            "status": "success"
        }
        
        logger.info(f"✅ API SUCCESS - Request completed in {request_time:.2f}s")
        return response_data
        
    except Exception as e:
        request_time = time.time() - request_start
        logger.error(f"❌ API ERROR - Failed after {request_time:.2f}s: {e}")
        
        return {
            "final_response": f"Error: {str(e)}",
            "najir_response": "",
            "act_response": "",
            "najir_sources": [],
            "act_sources": [],
            "suggested_sources": [],
            "total_filtered": 0,
            "total_suggestions": 0,
            "status": "error"
        }